#!/usr/bin/env python3
"""
Neural G1 - Notebook Test Script
Tests the main components of the Neural G1 training notebook

This script verifies:
1. All imports work correctly
2. Data loading functions work
3. Model architectures are valid
4. Training pipeline is functional

Author: Neural G1 Development Team
Date: 2025-06-14
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

def test_imports():
    """Test all required imports"""
    print("🧪 Testing imports...")
    
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import torch
        import torch.nn as nn
        import pytorch_lightning as pl
        from sklearn.preprocessing import StandardScaler
        print("✅ Core imports successful")
        
        # Test PyTorch setup
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"✅ PyTorch device: {device}")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_data_loading():
    """Test data loading function"""
    print("\n🧪 Testing data loading...")
    
    try:
        # Create sample data
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Generate sample XAUUSD data
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)
        
        # Simulate realistic gold price data
        base_price = 2000
        price_changes = np.random.normal(0, 20, len(dates))
        prices = base_price + np.cumsum(price_changes)
        
        sample_data = pd.DataFrame({
            'DateTime': dates,
            'Open': prices + np.random.normal(0, 5, len(dates)),
            'High': prices + np.random.uniform(5, 15, len(dates)),
            'Low': prices - np.random.uniform(5, 15, len(dates)),
            'Close': prices,
            'Volume': np.random.randint(100, 2000, len(dates))
        })
        
        # Save sample data
        sample_data.to_csv('test_data.csv', index=False)
        print("✅ Sample data created")
        
        # Test loading function (simplified version)
        def load_test_data(file_path):
            df = pd.read_csv(file_path)
            df['DateTime'] = pd.to_datetime(df['DateTime'])
            df.set_index('DateTime', inplace=True)
            return df
        
        loaded_data = load_test_data('test_data.csv')
        print(f"✅ Data loading successful: {len(loaded_data)} rows")
        
        # Clean up
        os.remove('test_data.csv')
        
        return True, loaded_data
    except Exception as e:
        print(f"❌ Data loading test failed: {e}")
        return False, None

def test_technical_indicators(data):
    """Test technical indicators"""
    print("\n🧪 Testing technical indicators...")
    
    try:
        # Add basic indicators
        data['SMA_20'] = data['Close'].rolling(window=20).mean()
        data['RSI_14'] = calculate_rsi(data['Close'])
        
        # Check if indicators were added
        indicator_count = len([col for col in data.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume']])
        print(f"✅ Technical indicators added: {indicator_count} indicators")
        
        return True
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        return False

def calculate_rsi(prices, period=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def test_model_architectures():
    """Test model architectures"""
    print("\n🧪 Testing model architectures...")
    
    try:
        import torch
        import torch.nn as nn
        import pytorch_lightning as pl
        
        # Test TFT model (simplified)
        class SimpleTFT(pl.LightningModule):
            def __init__(self, input_size, hidden_size=64):
                super().__init__()
                self.input_projection = nn.Linear(input_size, hidden_size)
                self.transformer = nn.TransformerEncoder(
                    nn.TransformerEncoderLayer(hidden_size, nhead=4, batch_first=True),
                    num_layers=2
                )
                self.output = nn.Linear(hidden_size, 1)
            
            def forward(self, x):
                x = self.input_projection(x)
                x = self.transformer(x)
                return self.output(x[:, -1, :])
            
            def training_step(self, batch, batch_idx):
                x, y = batch
                y_hat = self(x)
                loss = nn.MSELoss()(y_hat, y)
                return loss
            
            def configure_optimizers(self):
                return torch.optim.Adam(self.parameters())
        
        # Test model initialization
        model = SimpleTFT(input_size=10)
        print("✅ TFT model architecture valid")
        
        # Test forward pass
        sample_input = torch.randn(2, 60, 10)  # batch_size=2, seq_len=60, features=10
        output = model(sample_input)
        print(f"✅ Forward pass successful: output shape {output.shape}")
        
        return True
    except Exception as e:
        print(f"❌ Model architecture test failed: {e}")
        return False

def test_training_pipeline():
    """Test training pipeline components"""
    print("\n🧪 Testing training pipeline...")
    
    try:
        import torch
        from torch.utils.data import Dataset, DataLoader
        
        # Test dataset class
        class TestDataset(Dataset):
            def __init__(self, size=100):
                self.data = torch.randn(size, 60, 10)
                self.targets = torch.randn(size, 1)
            
            def __len__(self):
                return len(self.data)
            
            def __getitem__(self, idx):
                return self.data[idx], self.targets[idx]
        
        # Test data loader
        dataset = TestDataset()
        dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
        
        # Test batch
        batch = next(iter(dataloader))
        x, y = batch
        print(f"✅ Training pipeline components work: batch shapes {x.shape}, {y.shape}")
        
        return True
    except Exception as e:
        print(f"❌ Training pipeline test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧠 Neural G1 - Notebook Component Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Data loading
    data_test_passed, sample_data = test_data_loading()
    if data_test_passed:
        tests_passed += 1
    
    # Test 3: Technical indicators
    if sample_data is not None and test_technical_indicators(sample_data):
        tests_passed += 1
    
    # Test 4: Model architectures
    if test_model_architectures():
        tests_passed += 1
    
    # Test 5: Training pipeline
    if test_training_pipeline():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Neural G1 notebook is ready to use.")
        return True
    else:
        print(f"⚠️ {total_tests - tests_passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
