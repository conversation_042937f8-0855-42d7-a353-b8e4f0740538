# 🧠 Neural G1 - CSV Normalization Instructions

## 📋 Overview
This guide explains how to normalize your XAUUSD CSV files for consistent, reliable training in Google Colab.

## 🔧 Step 1: Run the Normalization Script

### **Prerequisites:**
- Python 3.7+ installed
- pandas library: `pip install pandas`
- Your original CSV files in the same directory

### **Run the Script:**
```bash
python normalize_csv_files.py
```

### **What it does:**
- ✅ Reads your tab-separated CSV files
- ✅ Standardizes column format to: `DateTime,Open,High,Low,Close,Volume`
- ✅ Converts to proper datetime format
- ✅ Removes invalid/duplicate data
- ✅ Validates OHLC data integrity
- ✅ Saves normalized files in `normalized_data/` folder

### **Expected Output:**
```
🧠 Neural G1 - CSV File Normalizer
==================================================
📂 Processing M1: XAUUSD_M1.csv
   📊 Original shape: (200000, 7)
   📊 Original columns: ['Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'Unnamed: 6']
   📅 Sample Time values: ['2008-10-01 00:00:00', '2008-10-01 00:01:00', ...]
   ✅ Normalized M1:
      📊 Rows: 200,000
      📅 Period: 2008-10-01 to 2024-12-31
      💰 Price range: $681.99 - $2790.07
      💾 Saved to: normalized_data/XAUUSD_M1_normalized.csv
```

## 📤 Step 2: Upload to Google Drive

### **Upload Location:**
Upload the normalized files to:
```
/content/drive/MyDrive/Neural_G1/normalized_data/
```

### **Required Files:**
- `XAUUSD_M1_normalized.csv`
- `XAUUSD_M5_normalized.csv`
- `XAUUSD_M15_normalized.csv`
- `XAUUSD_M30_normalized.csv`
- `XAUUSD_H1_normalized.csv`
- `XAUUSD_H4_normalized.csv`
- `XAUUSD_D1_normalized.csv`

### **File Structure in Google Drive:**
```
Neural_G1/
├── normalized_data/
│   ├── XAUUSD_M1_normalized.csv
│   ├── XAUUSD_M5_normalized.csv
│   ├── XAUUSD_M15_normalized.csv
│   ├── XAUUSD_M30_normalized.csv
│   ├── XAUUSD_H1_normalized.csv
│   ├── XAUUSD_H4_normalized.csv
│   └── XAUUSD_D1_normalized.csv
└── Neural_G1_Training_Notebook.ipynb
```

## 🚀 Step 3: Run Updated Colab Notebook

### **What's Changed:**
- ✅ New `load_normalized_forex_data()` function
- ✅ Updated file paths to use normalized files
- ✅ Improved error handling and validation
- ✅ Faster loading (no complex parsing needed)

### **Expected Colab Output:**
```
📊 Loading NORMALIZED XAUUSD data for all timeframes...
💡 Using pre-normalized CSV files from Google Drive

🔄 Loading normalized M1 data...
📂 Loading normalized M1 from /content/drive/MyDrive/Neural_G1/normalized_data/XAUUSD_M1_normalized.csv...
📊 Shape: (200000, 6)
📊 Columns: ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']
📅 Sample DateTime values: ['2008-10-01 00:00:00', '2008-10-01 00:01:00', ...]
✅ Successfully loaded normalized M1:
   📊 Rows: 200,000
   📅 Period: 2008-10-01 to 2024-12-31
   💰 Price range: $681.99 - $2790.07
   📈 Avg Volume: 1,250
```

## 🎯 Benefits of Normalization

### **Before (Original Files):**
- ❌ Tab-separated format
- ❌ Extra columns causing confusion
- ❌ Inconsistent datetime parsing
- ❌ 1970 date errors
- ❌ Mixed data types

### **After (Normalized Files):**
- ✅ Standard CSV format (comma-separated)
- ✅ Clean 6-column structure
- ✅ Consistent datetime format
- ✅ Proper date ranges (2008-2024)
- ✅ Validated numeric data
- ✅ No duplicate/invalid rows
- ✅ Faster loading in Colab

## 🔍 Normalized File Format

### **Column Structure:**
```csv
DateTime,Open,High,Low,Close,Volume
2008-10-01 00:00:00,875.01,892.86,864.216,866.484,751
2008-10-02 00:00:00,866.638,875.227,828.81,832.404,1398
2008-10-03 00:00:00,832.166,846.527,820.199,833.342,1255
...
```

### **Data Types:**
- `DateTime`: pandas datetime64[ns]
- `Open`: float64
- `High`: float64
- `Low`: float64
- `Close`: float64
- `Volume`: int64

## 🛠️ Troubleshooting

### **If normalization fails:**
1. Check file permissions
2. Ensure pandas is installed: `pip install pandas`
3. Verify original CSV files exist
4. Check Python version (3.7+ required)

### **If Colab can't find files:**
1. Verify Google Drive is mounted
2. Check file paths match exactly
3. Ensure files are uploaded to correct folder
4. Refresh Google Drive connection

### **If data looks wrong:**
1. Check original CSV file format
2. Verify datetime values in normalized files
3. Compare price ranges with original data
4. Check for missing timeframes

## 📞 Support

If you encounter issues:
1. Check the console output for specific error messages
2. Verify file paths and permissions
3. Ensure all required files are present
4. Test with a single timeframe first (D1 recommended)

---

**Ready to proceed? Run the normalization script and upload the files!** 🚀
