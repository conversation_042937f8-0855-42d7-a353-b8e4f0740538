# GPU Diagnostic and Optimization Cell
# Add this cell to your notebook to check and optimize GPU usage

import torch
import time

def check_gpu_status():
    """
    Comprehensive GPU status check and optimization
    """
    print("🔥 Neural G1 GPU Diagnostic")
    print("=" * 50)
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("❌ CUDA/GPU NOT AVAILABLE!")
        print("\n🔧 To enable GPU in Google Colab:")
        print("1. Go to Runtime → Change runtime type")
        print("2. Select 'GPU' as Hardware accelerator")
        print("3. Choose 'T4 GPU' (recommended)")
        print("4. Click 'Save' and restart runtime")
        print("5. Re-run all cells")
        return False
    
    # GPU Information
    print(f"✅ CUDA Available: True")
    print(f"✅ GPU Device: {torch.cuda.get_device_name(0)}")
    print(f"✅ CUDA Version: {torch.version.cuda}")
    print(f"✅ PyTorch Version: {torch.__version__}")
    print(f"✅ GPU Count: {torch.cuda.device_count()}")
    
    # Memory Information
    gpu_props = torch.cuda.get_device_properties(0)
    total_memory = gpu_props.total_memory / 1024**3
    allocated = torch.cuda.memory_allocated(0) / 1024**3
    cached = torch.cuda.memory_reserved(0) / 1024**3
    free = total_memory - cached
    
    print(f"\n📊 GPU Memory Status:")
    print(f"   Total Memory: {total_memory:.1f} GB")
    print(f"   Allocated: {allocated:.2f} GB")
    print(f"   Cached: {cached:.2f} GB")
    print(f"   Free: {free:.2f} GB")
    print(f"   Usage: {(cached/total_memory)*100:.1f}%")
    
    # Performance Test
    print(f"\n🧪 GPU Performance Test:")
    
    # Clear cache first
    torch.cuda.empty_cache()
    
    # Test 1: Matrix Multiplication
    start_time = time.time()
    test_tensor = torch.randn(1024, 1024, device='cuda')
    result = torch.matmul(test_tensor, test_tensor.T)
    gpu_time = time.time() - start_time
    print(f"   Matrix Mult (1024x1024): {gpu_time:.3f}s")
    
    # Test 2: Mixed Precision
    start_time = time.time()
    with torch.cuda.amp.autocast():
        fp16_result = torch.matmul(test_tensor.half(), test_tensor.half().T)
    fp16_time = time.time() - start_time
    print(f"   Mixed Precision (FP16): {fp16_time:.3f}s")
    print(f"   Speedup with FP16: {gpu_time/fp16_time:.1f}x")
    
    # Clean up test tensors
    del test_tensor, result, fp16_result
    torch.cuda.empty_cache()
    
    # Set optimizations
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    print(f"\n⚡ GPU Optimizations:")
    print(f"   cudnn.benchmark: {torch.backends.cudnn.benchmark}")
    print(f"   cudnn.deterministic: {torch.backends.cudnn.deterministic}")
    
    print(f"\n🎯 GPU STATUS: READY FOR TRAINING!")
    return True

def monitor_training_gpu():
    """
    Monitor GPU during training
    """
    if not torch.cuda.is_available():
        return
    
    allocated = torch.cuda.memory_allocated(0) / 1024**3
    cached = torch.cuda.memory_reserved(0) / 1024**3
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"🔥 Training GPU Usage:")
    print(f"   Allocated: {allocated:.2f} GB")
    print(f"   Cached: {cached:.2f} GB") 
    print(f"   Free: {total-cached:.2f} GB")
    print(f"   Usage: {(cached/total)*100:.1f}%")
    
    if cached / total > 0.9:
        print("⚠️ WARNING: GPU memory usage high!")
        print("   Consider reducing batch size or clearing cache")
        print("   Run: torch.cuda.empty_cache()")

# Run the diagnostic
gpu_ready = check_gpu_status()

if gpu_ready:
    print("\n" + "=" * 50)
    print("🚀 GPU READY FOR NEURAL G1 TRAINING!")
    print("✅ All optimizations enabled")
    print("✅ Mixed precision available")
    print("✅ Memory optimized")
    print("=" * 50)
    
    # Show how to monitor during training
    print("\n📋 During training, run this to monitor GPU:")
    print("monitor_training_gpu()")
    
else:
    print("\n" + "=" * 50)
    print("⚠️ GPU NOT AVAILABLE - TRAINING WILL BE SLOW")
    print("🔧 Follow the instructions above to enable GPU")
    print("=" * 50)
